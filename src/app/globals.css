@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.13 0.028 261.692);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.13 0.028 261.692);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.13 0.028 261.692);
  --primary: oklch(0.21 0.034 264.665);
  --primary-foreground: oklch(0.985 0.002 247.839);
  --secondary: oklch(0.967 0.003 264.542);
  --secondary-foreground: oklch(0.21 0.034 264.665);
  --muted: oklch(0.967 0.003 264.542);
  --muted-foreground: oklch(0.551 0.027 264.364);
  --accent: oklch(0.967 0.003 264.542);
  --accent-foreground: oklch(0.21 0.034 264.665);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.928 0.006 264.531);
  --input: oklch(0.928 0.006 264.531);
  --ring: oklch(0.707 0.022 261.325);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0.002 247.839);
  --sidebar-foreground: oklch(0.13 0.028 261.692);
  --sidebar-primary: oklch(0.21 0.034 264.665);
  --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
  --sidebar-accent: oklch(0.967 0.003 264.542);
  --sidebar-accent-foreground: oklch(0.21 0.034 264.665);
  --sidebar-border: oklch(0.928 0.006 264.531);
  --sidebar-ring: oklch(0.707 0.022 261.325);
}

.dark {
  /* Material Design Dark Theme - Surface Hierarchy */
  --background: oklch(0.12 0 0);
  /* Primary background - darkest surface */
  --foreground: oklch(0.98 0 0);
  /* High emphasis text (87% opacity equivalent) */

  /* Surface elevations following Material Design */
  --card: oklch(0.15 0 0);
  /* Elevated surface level 1 */
  --card-foreground: oklch(0.98 0 0);
  /* High emphasis text on surfaces */

  --popover: oklch(0.15 0 0);
  /* Same as card for consistency */
  --popover-foreground: oklch(0.98 0 0);

  /* Primary color - keep brand color but ensure dark theme contrast */
  --primary: oklch(0.75 0.08 264);
  /* Adjusted primary with slight blue tint for dark */
  --primary-foreground: oklch(0.12 0 0);
  /* Dark text on primary */

  /* Secondary and accent colors */
  --secondary: oklch(0.2 0 0);
  /* Secondary surface level */
  --secondary-foreground: oklch(0.98 0 0);

  /* Muted elements - medium emphasis (60% opacity equivalent) */
  --muted: oklch(0.18 0 0);
  /* Muted surface */
  --muted-foreground: oklch(0.7 0 0);
  /* Medium emphasis text */

  --accent: oklch(0.25 0 0);
  /* Accent surface */
  --accent-foreground: oklch(0.98 0 0);

  /* Error/destructive - maintain accessibility */
  --destructive: oklch(0.6 0.25 25);
  /* Red with good contrast */
  --destructive-foreground: oklch(0.98 0 0);

  /* Interactive elements */
  --border: oklch(0.25 0 0);
  /* Border color */
  --input: oklch(0.18 0 0);
  /* Input background */
  --ring: oklch(0.75 0.08 264);
  /* Focus ring matches primary */

  /* Chart colors - adapted for dark theme */
  --chart-1: oklch(0.7 0.15 25);
  /* Primary chart color */
  --chart-2: oklch(0.75 0.12 180);
  /* Secondary chart color */
  --chart-3: oklch(0.8 0.1 70);
  /* Tertiary chart color */
  --chart-4: oklch(0.65 0.18 300);
  /* Quaternary chart color */
  --chart-5: oklch(0.6 0.2 15);
  /* Quinary chart color */

  /* Sidebar - elevated surface */
  --sidebar: oklch(0.14 0 0);
  /* Sidebar background */
  --sidebar-foreground: oklch(0.98 0 0);
  /* Sidebar text */
  --sidebar-primary: oklch(0.75 0.08 264);
  /* Sidebar primary */
  --sidebar-primary-foreground: oklch(0.12 0 0);
  --sidebar-accent: oklch(0.22 0 0);
  /* Sidebar accent */
  --sidebar-accent-foreground: oklch(0.98 0 0);
  --sidebar-border: oklch(0.2 0 0);
  /* Sidebar borders */
  --sidebar-ring: oklch(0.75 0.08 264);
  /* Sidebar focus */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}