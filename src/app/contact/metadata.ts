import type { Metadata } from 'next'

export const metadata: Metadata = {
    title: 'Contact Ramblers Media - Get Your Video Production Quote | Dallas, TX',
    description: 'Ready to start your video production project? Contact Ramblers Media for a free quote. Professional video production services in Dallas, TX with government contractor experience.',
    keywords: 'contact Ramblers Media, video production quote, Dallas video production, government contractor contact, professional filming services, video production consultation',
    openGraph: {
        title: 'Contact Ramblers Media - Get Your Video Production Quote | Dallas, TX',
        description: 'Ready to start your video production project? Contact Ramblers Media for a free quote. Professional video production services in Dallas, TX.',
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Contact Ramblers Media - Get Your Video Production Quote | Dallas, TX',
        description: 'Ready to start your video production project? Contact Ramblers Media for a free quote.',
    },
}
